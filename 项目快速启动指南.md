# QMT自动交易系统项目快速启动指南

## 🚀 立即开始（第一天行动清单）

### ✅ 上午任务（9:00-12:00）

#### 1. 环境准备（30分钟）
- [ ] 确认已安装国金证券QMT平台
- [ ] 检查QMT版本（建议最新版本）
- [ ] 确认交易账户可正常登录
- [ ] 测试QMT基本功能

#### 2. 开发环境搭建（60分钟）
- [ ] 安装Python 3.7或3.8（推荐3.8）
- [ ] 配置Python环境变量
- [ ] 安装必要的库：
```bash
pip install pandas numpy json datetime time
```
- [ ] 测试Python环境是否正常

#### 3. QMT API测试（90分钟）
- [ ] 在QMT中新建一个测试策略
- [ ] 复制以下测试代码：

```python
#coding:gbk

def init(ContextInfo):
    print("=== QMT API测试开始 ===")
    
    # 测试基本功能
    try:
        from xtquant import xtdata
        print("xtdata模块导入成功")
        
        # 测试获取股票列表
        stocks = xtdata.get_stock_list_in_sector("沪深300")
        if stocks:
            print("获取沪深300成分股成功，共%d只" % len(stocks))
        else:
            print("获取股票列表失败")
            
    except Exception as e:
        print("API测试失败: %s" % str(e))
    
    print("=== 初始化完成 ===")

def handlebar(ContextInfo):
    print("策略正在运行...")
```

- [ ] 编译并运行测试策略
- [ ] 确认输出正常

### ✅ 下午任务（14:00-18:00）

#### 4. 同花顺数据测试（60分钟）
- [ ] 打开同花顺软件
- [ ] 创建一个测试板块（如"测试板块"）
- [ ] 添加几只股票到测试板块
- [ ] 导出板块数据，确认文件格式
- [ ] 记录板块文件保存路径

#### 5. 项目结构创建（30分钟）
创建以下目录结构：
```
QMT_AutoTrading/
├── src/                    # 源代码目录
│   ├── data_manager.py     # 数据管理模块
│   ├── trading_engine.py   # 交易引擎
│   ├── risk_manager.py     # 风险管理
│   └── logger.py           # 日志管理
├── config/                 # 配置文件目录
│   └── config.json         # 主配置文件
├── logs/                   # 日志目录
├── data/                   # 数据目录
└── tests/                  # 测试目录
```

#### 6. 基础配置文件创建（30分钟）
创建 `config/config.json`：
```json
{
    "trading": {
        "max_single_stock_amount": 5000,
        "max_daily_loss": 2000,
        "stop_loss_ratio": 0.03,
        "stop_profit_ratio": 0.08
    },
    "data": {
        "sector_path": "C:/同花顺/板块数据/",
        "update_interval": 5
    },
    "risk": {
        "max_position_ratio": 0.1,
        "trading_start": "09:30:00",
        "trading_end": "15:00:00"
    }
}
```

#### 7. 第一个功能模块开发（120分钟）
创建 `src/data_manager.py`：
```python
#coding:gbk

import json
import os
import time
from datetime import datetime

class DataManager:
    def __init__(self, config_path):
        self.config = self.load_config(config_path)
        self.sector_data = {}
        
    def load_config(self, config_path):
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print("配置文件加载失败: %s" % str(e))
            return {}
    
    def read_sector_file(self, file_path):
        """读取同花顺板块文件"""
        try:
            stocks = []
            with open(file_path, 'r', encoding='gbk') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        # 解析股票代码
                        if '.' in line:
                            stocks.append(line)
            return stocks
        except Exception as e:
            print("读取板块文件失败: %s" % str(e))
            return []
    
    def get_all_sectors(self):
        """获取所有板块数据"""
        sector_path = self.config.get('data', {}).get('sector_path', '')
        if not os.path.exists(sector_path):
            print("板块数据路径不存在: %s" % sector_path)
            return {}
        
        sectors = {}
        try:
            for file_name in os.listdir(sector_path):
                if file_name.endswith('.blk'):
                    sector_name = file_name[:-4]  # 去掉.blk扩展名
                    file_path = os.path.join(sector_path, file_name)
                    stocks = self.read_sector_file(file_path)
                    if stocks:
                        sectors[sector_name] = stocks
                        print("加载板块 %s: %d只股票" % (sector_name, len(stocks)))
        except Exception as e:
            print("获取板块数据失败: %s" % str(e))
        
        return sectors
    
    def is_trading_time(self):
        """检查是否在交易时间内"""
        now = datetime.now()
        current_time = now.strftime("%H:%M:%S")
        
        # 检查是否为工作日
        if now.weekday() >= 5:  # 周六日
            return False
        
        # 检查交易时间
        trading_start = self.config.get('risk', {}).get('trading_start', '09:30:00')
        trading_end = self.config.get('risk', {}).get('trading_end', '15:00:00')
        
        if trading_start <= current_time <= trading_end:
            # 排除午休时间
            if '11:30:00' <= current_time <= '13:00:00':
                return False
            return True
        
        return False

# 测试代码
if __name__ == "__main__":
    dm = DataManager('../config/config.json')
    print("交易时间检查:", dm.is_trading_time())
    sectors = dm.get_all_sectors()
    print("获取到板块数量:", len(sectors))
```

## 📋 第一周详细计划

### 第1天（今天）
- [x] 完成上述快速启动任务
- [ ] 测试基础功能
- [ ] 记录遇到的问题

### 第2天
- [ ] 完善数据管理模块
- [ ] 开始交易引擎模块开发
- [ ] 编写单元测试

### 第3天
- [ ] 完成交易引擎基础功能
- [ ] 集成数据管理和交易引擎
- [ ] 测试模块间通信

### 第4天
- [ ] 开发风险管理模块
- [ ] 实现基础风险控制逻辑
- [ ] 测试风险控制功能

### 第5天
- [ ] 完善日志系统
- [ ] 集成所有模块
- [ ] 进行端到端测试

## 🔧 常见问题解决

### Q1: QMT策略编译失败
**解决方案：**
1. 检查代码开头是否有 `#coding:gbk`
2. 确认函数名为 `init(ContextInfo)` 和 `handlebar(ContextInfo)`
3. 检查代码语法是否正确

### Q2: 无法获取板块数据
**解决方案：**
1. 确认同花顺软件已正确安装
2. 检查板块文件路径是否正确
3. 确认板块文件格式是否正确

### Q3: Python库导入失败
**解决方案：**
1. 确认Python版本兼容（推荐3.7-3.8）
2. 使用QMT内置的Python环境
3. 检查库是否正确安装

## 📞 紧急联系方式

### 技术支持
- QMT官方技术支持
- 国金证券客服热线
- 同花顺技术支持

### 项目团队
- 项目经理：[联系方式]
- 技术负责人：[联系方式]
- 测试负责人：[联系方式]

## 📈 成功指标

### 第一天目标
- [ ] QMT环境正常运行
- [ ] Python开发环境配置完成
- [ ] 基础API测试通过
- [ ] 项目结构创建完成

### 第一周目标
- [ ] 数据管理模块开发完成
- [ ] 交易引擎基础功能实现
- [ ] 风险管理模块框架搭建
- [ ] 基础测试用例编写完成

## 🎯 下一步行动

完成今天的任务后，请：
1. 更新项目进度跟踪表
2. 记录遇到的问题和解决方案
3. 准备明天的开发任务
4. 与团队成员同步进度

---

**重要提醒：**
- 所有代码都要先在测试环境验证
- 实盘交易前必须经过充分测试
- 遇到问题及时记录和求助
- 保持代码和文档的同步更新
