#coding:gbk

"""
QMT涨停双响炮自动交易系统
完整版单文件实现

功能特点：
1. 通过QMT API直接获取自定义板块"涨停双响炮刚启动"
2. 实时监控板块股票变化，自动执行买卖操作
3. 完整的风险控制和资金管理
4. 详细的交易日志和统计功能
5. 支持XML界面配置

使用说明：
1. 在QMT中新建Python策略，复制此代码
2. 确保已创建名为"涨停双响炮刚启动"的自定义板块
3. 配置下方的参数设置
4. 编译并运行策略

注意：默认为模拟模式，实盘前请充分测试
"""

import time
import datetime
import json
import os
from typing import Dict, List, Set, Any, Optional

# ==================== 系统配置参数 ====================
class SystemConfig:
    """系统配置类 - 集中管理所有配置参数"""

    # 核心配置
    TARGET_SECTOR_NAME = "涨停双响炮刚启动"  # 目标板块名称（仅用于显示）
    ENABLE_REAL_TRADING = False              # 是否启用实盘交易（False=模拟模式）

    # 目标股票列表配置（手动维护）
    # 由于QMT API无法获取用户自定义板块，改为手动配置股票列表
    TARGET_STOCKS = [
        # 请在这里添加您要监控的股票代码
        # 示例：
        # "000001.SZ",  # 平安银行
        # "000002.SZ",  # 万科A
        # "600000.SH",  # 浦发银行
        # "600036.SH",  # 招商银行
    ]

    # 交易配置
    MAX_SINGLE_STOCK_AMOUNT = 10000         # 单只股票最大买入金额
    MAX_DAILY_LOSS = 5000                   # 每日最大亏损限制
    MAX_POSITION_RATIO = 0.15               # 单只股票最大仓位比例
    STOP_LOSS_RATIO = 0.05                  # 止损比例（5%）
    STOP_PROFIT_RATIO = 0.12                # 止盈比例（12%）
    
    # 时间配置
    TRADING_START_TIME = "09:30:00"         # 交易开始时间
    TRADING_END_TIME = "14:55:00"           # 交易结束时间（提前5分钟）
    LUNCH_BREAK_START = "11:30:00"          # 午休开始时间
    LUNCH_BREAK_END = "13:00:00"            # 午休结束时间
    
    # 数据更新配置
    DATA_UPDATE_INTERVAL = 3                # 数据更新间隔（秒）
    STATUS_REPORT_INTERVAL = 60             # 状态报告间隔（秒）
    
    # 风险控制配置
    MAX_TRADES_PER_DAY = 30                 # 每日最大交易次数
    MIN_STOCK_PRICE = 3.0                   # 最低股价限制
    MAX_STOCK_PRICE = 200.0                 # 最高股价限制
    EXCLUDE_ST_STOCKS = True                # 是否排除ST股票
    
    # 资金管理配置
    TOTAL_CAPITAL = 100000                  # 总资金（用于计算仓位）
    EMERGENCY_STOP_LOSS = 0.20              # 紧急止损比例（20%）

# ==================== 数据管理模块 ====================
class DataManager:
    """数据管理器 - 负责获取和处理板块数据"""
    
    def __init__(self):
        self.sector_data = {}
        self.last_sector_data = {}
        self.last_update_time = 0
        self.update_count = 0
        
    def is_trading_time(self) -> bool:
        """检查是否在交易时间内"""
        try:
            now = datetime.datetime.now()
            current_time = now.strftime("%H:%M:%S")
            
            # 检查是否为工作日
            if now.weekday() >= 5:
                return False
            
            # 检查是否在交易时间段内
            if SystemConfig.TRADING_START_TIME <= current_time <= SystemConfig.TRADING_END_TIME:
                # 排除午休时间
                if SystemConfig.LUNCH_BREAK_START <= current_time <= SystemConfig.LUNCH_BREAK_END:
                    return False
                return True
            
            return False
            
        except Exception as e:
            print("检查交易时间异常: %s" % str(e))
            return False
    
    def get_target_stocks(self) -> List[str]:
        """获取目标股票列表（从配置中读取）"""
        try:
            # 从配置中获取股票列表
            stocks = SystemConfig.TARGET_STOCKS.copy()

            if not stocks:
                print("警告: 目标股票列表为空，请在SystemConfig.TARGET_STOCKS中配置股票代码")
                print("示例配置:")
                print('TARGET_STOCKS = [')
                print('    "000001.SZ",  # 平安银行')
                print('    "000002.SZ",  # 万科A')
                print('    "600000.SH",  # 浦发银行')
                print(']')
                return []

            # 验证股票代码格式
            valid_stocks = []
            for stock_code in stocks:
                if self.validate_stock_code(stock_code):
                    valid_stocks.append(stock_code)
                else:
                    print("警告: 股票代码格式无效: %s" % stock_code)

            if valid_stocks:
                print("成功加载目标股票列表: %d只股票" % len(valid_stocks))
                print("股票列表: %s" % ', '.join(valid_stocks))
            else:
                print("错误: 没有有效的股票代码")

            return valid_stocks

        except Exception as e:
            print("获取目标股票列表异常: %s" % str(e))
            return []
    
    def update_sector_data(self) -> bool:
        """更新目标股票数据"""
        try:
            current_time = time.time()

            # 检查更新间隔
            if current_time - self.last_update_time < SystemConfig.DATA_UPDATE_INTERVAL:
                return True

            # 获取目标股票列表
            stocks = self.get_target_stocks()

            if stocks:
                self.sector_data = {SystemConfig.TARGET_SECTOR_NAME: stocks}
                self.last_update_time = current_time
                self.update_count += 1

                print("目标股票数据更新成功 (第%d次): %s - %d只股票" %
                      (self.update_count, SystemConfig.TARGET_SECTOR_NAME, len(stocks)))
                return True
            else:
                print("目标股票数据更新失败: 请检查SystemConfig.TARGET_STOCKS配置")
                return False

        except Exception as e:
            print("更新目标股票数据异常: %s" % str(e))
            return False
    
    def detect_stock_changes(self) -> Dict[str, Dict[str, List[str]]]:
        """检测目标股票列表变化"""
        changes = {}

        try:
            current_stocks = set(self.sector_data.get(SystemConfig.TARGET_SECTOR_NAME, []))
            last_stocks = set(self.last_sector_data.get(SystemConfig.TARGET_SECTOR_NAME, []))

            # 首次运行
            if not self.last_sector_data:
                self.last_sector_data = self.sector_data.copy()
                if current_stocks:
                    print("首次加载目标股票: %d只股票" % len(current_stocks))
                    print("股票列表: %s" % ', '.join(sorted(current_stocks)))
                else:
                    print("警告: 目标股票列表为空，请在SystemConfig.TARGET_STOCKS中配置")
                return changes

            # 检测变化
            added_stocks = list(current_stocks - last_stocks)
            removed_stocks = list(last_stocks - current_stocks)

            if added_stocks or removed_stocks:
                changes[SystemConfig.TARGET_SECTOR_NAME] = {
                    'added': added_stocks,
                    'removed': removed_stocks
                }

                if added_stocks:
                    print("检测到新增股票: %s" % ', '.join(added_stocks))
                if removed_stocks:
                    print("检测到移除股票: %s" % ', '.join(removed_stocks))

            # 更新历史数据
            self.last_sector_data = self.sector_data.copy()

        except Exception as e:
            print("检测股票变化异常: %s" % str(e))

        return changes
    
    def validate_stock_code(self, stock_code: str) -> bool:
        """验证股票代码格式"""
        try:
            if not stock_code or '.' not in stock_code:
                return False
            
            code, market = stock_code.split('.')
            
            if len(code) != 6 or not code.isdigit():
                return False
            
            if market not in ['SH', 'SZ']:
                return False
            
            return True
            
        except Exception:
            return False

# ==================== 交易引擎模块 ====================
class TradingEngine:
    """交易引擎 - 负责交易决策和执行"""
    
    def __init__(self):
        self.today_bought_stocks = set()
        self.today_sold_stocks = set()
        self.current_positions = {}  # {stock_code: {'quantity': int, 'avg_price': float, 'timestamp': str}}
        self.trade_history = []
        self.trade_count = 0
        
    def should_buy_stock(self, stock_code: str) -> bool:
        """判断是否应该买入股票"""
        try:
            # 检查今日是否已买入
            if stock_code in self.today_bought_stocks:
                return False
            
            # 检查是否已持仓
            if stock_code in self.current_positions:
                return False
            
            # 检查每日交易次数限制
            if self.trade_count >= SystemConfig.MAX_TRADES_PER_DAY:
                print("已达到每日最大交易次数限制: %d" % SystemConfig.MAX_TRADES_PER_DAY)
                return False
            
            return True
            
        except Exception as e:
            print("判断买入条件异常: %s" % str(e))
            return False
    
    def should_sell_stock(self, stock_code: str) -> bool:
        """判断是否应该卖出股票"""
        try:
            # 检查是否持仓
            if stock_code not in self.current_positions:
                return False
            
            # 检查今日是否已卖出
            if stock_code in self.today_sold_stocks:
                return False
            
            return True
            
        except Exception as e:
            print("判断卖出条件异常: %s" % str(e))
            return False
    
    def calculate_buy_quantity(self, stock_code: str, price: float) -> int:
        """计算买入数量"""
        try:
            # 基于最大金额计算数量
            max_amount = SystemConfig.MAX_SINGLE_STOCK_AMOUNT
            quantity = int(max_amount / price / 100) * 100  # 向下取整到100的倍数
            
            # 最少1手
            quantity = max(100, quantity)
            
            # 检查仓位限制
            max_position_value = SystemConfig.TOTAL_CAPITAL * SystemConfig.MAX_POSITION_RATIO
            max_quantity_by_position = int(max_position_value / price / 100) * 100
            
            quantity = min(quantity, max_quantity_by_position)
            
            return quantity
            
        except Exception as e:
            print("计算买入数量异常: %s" % str(e))
            return 100
    
    def execute_buy_order(self, stock_code: str) -> bool:
        """执行买入订单"""
        try:
            # 模拟获取当前价格（实际应该从行情API获取）
            current_price = 10.0  # 这里应该调用实际的行情API
            
            # 计算买入数量
            quantity = self.calculate_buy_quantity(stock_code, current_price)
            amount = current_price * quantity
            
            # 执行买入逻辑
            if SystemConfig.ENABLE_REAL_TRADING:
                # 实盘交易代码
                success = self.place_real_buy_order(stock_code, quantity, current_price)
            else:
                # 模拟交易
                success = True
                print("模拟买入: %s, 价格: %.2f, 数量: %d, 金额: %.2f" % 
                      (stock_code, current_price, quantity, amount))
            
            if success:
                # 记录交易
                trade_record = {
                    'timestamp': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'stock_code': stock_code,
                    'action': 'BUY',
                    'price': current_price,
                    'quantity': quantity,
                    'amount': amount,
                    'reason': 'sector_new_stock',
                    'status': 'SIMULATED' if not SystemConfig.ENABLE_REAL_TRADING else 'REAL'
                }
                
                self.trade_history.append(trade_record)
                
                # 更新持仓
                self.current_positions[stock_code] = {
                    'quantity': quantity,
                    'avg_price': current_price,
                    'timestamp': trade_record['timestamp']
                }
                
                # 记录今日买入
                self.today_bought_stocks.add(stock_code)
                self.trade_count += 1
                
                return True
            
            return False
            
        except Exception as e:
            print("执行买入订单异常: %s" % str(e))
            return False
    
    def execute_sell_order(self, stock_code: str, reason: str = "sector_remove") -> bool:
        """执行卖出订单"""
        try:
            if stock_code not in self.current_positions:
                return False
            
            position = self.current_positions[stock_code]
            quantity = position['quantity']
            buy_price = position['avg_price']
            
            # 模拟获取当前价格
            current_price = buy_price * 1.03  # 假设上涨3%
            amount = current_price * quantity
            profit = (current_price - buy_price) * quantity
            
            # 执行卖出逻辑
            if SystemConfig.ENABLE_REAL_TRADING:
                # 实盘交易代码
                success = self.place_real_sell_order(stock_code, quantity, current_price)
            else:
                # 模拟交易
                success = True
                print("模拟卖出: %s, 价格: %.2f, 数量: %d, 金额: %.2f, 盈亏: %.2f" % 
                      (stock_code, current_price, quantity, amount, profit))
            
            if success:
                # 记录交易
                trade_record = {
                    'timestamp': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'stock_code': stock_code,
                    'action': 'SELL',
                    'price': current_price,
                    'quantity': quantity,
                    'amount': amount,
                    'profit': profit,
                    'reason': reason,
                    'status': 'SIMULATED' if not SystemConfig.ENABLE_REAL_TRADING else 'REAL'
                }
                
                self.trade_history.append(trade_record)
                
                # 移除持仓
                del self.current_positions[stock_code]
                
                # 记录今日卖出
                self.today_sold_stocks.add(stock_code)
                self.trade_count += 1
                
                return True
            
            return False
            
        except Exception as e:
            print("执行卖出订单异常: %s" % str(e))
            return False
    
    def place_real_buy_order(self, stock_code: str, quantity: int, price: float) -> bool:
        """执行实盘买入订单"""
        try:
            # 这里应该调用QMT的实际交易API
            # 示例代码（需要根据实际QMT API调整）:
            # from xtquant.xttrader import XtQuantTrader
            # trader = XtQuantTrader()
            # order_id = trader.order_stock(account, stock_code, xtconstant.STOCK_BUY, quantity, xtconstant.PRICE_LIMIT, price)
            # return order_id > 0
            
            print("实盘买入功能待实现: %s" % stock_code)
            return False
            
        except Exception as e:
            print("实盘买入异常: %s" % str(e))
            return False
    
    def place_real_sell_order(self, stock_code: str, quantity: int, price: float) -> bool:
        """执行实盘卖出订单"""
        try:
            # 这里应该调用QMT的实际交易API
            print("实盘卖出功能待实现: %s" % stock_code)
            return False

        except Exception as e:
            print("实盘卖出异常: %s" % str(e))
            return False

    def get_today_stats(self) -> Dict[str, Any]:
        """获取今日统计"""
        today = datetime.datetime.now().strftime('%Y-%m-%d')
        today_trades = [t for t in self.trade_history
                       if t['timestamp'].startswith(today)]

        buy_count = len([t for t in today_trades if t['action'] == 'BUY'])
        sell_count = len([t for t in today_trades if t['action'] == 'SELL'])
        total_profit = sum(t.get('profit', 0) for t in today_trades if t['action'] == 'SELL')

        return {
            'date': today,
            'buy_count': buy_count,
            'sell_count': sell_count,
            'total_trades': len(today_trades),
            'total_profit': total_profit,
            'current_positions': len(self.current_positions),
            'trade_count': self.trade_count
        }

# ==================== 风险管理模块 ====================
class RiskManager:
    """风险管理器 - 负责风险控制和资金管理"""

    def __init__(self):
        self.daily_loss = 0.0
        self.risk_events = []
        self.emergency_stop = False

    def check_daily_loss_limit(self, current_loss: float) -> bool:
        """检查每日亏损限制"""
        try:
            if abs(current_loss) >= SystemConfig.MAX_DAILY_LOSS:
                self.risk_events.append({
                    'timestamp': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'event': 'DAILY_LOSS_LIMIT',
                    'description': '每日亏损超过限制',
                    'value': abs(current_loss),
                    'limit': SystemConfig.MAX_DAILY_LOSS
                })
                print("风险警告: 每日亏损 %.2f 超过限制 %.2f" %
                      (abs(current_loss), SystemConfig.MAX_DAILY_LOSS))
                return True

            return False

        except Exception as e:
            print("检查每日亏损限制异常: %s" % str(e))
            return False

    def check_emergency_stop(self, total_loss_ratio: float) -> bool:
        """检查是否需要紧急停止"""
        try:
            if total_loss_ratio >= SystemConfig.EMERGENCY_STOP_LOSS:
                self.emergency_stop = True
                self.risk_events.append({
                    'timestamp': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'event': 'EMERGENCY_STOP',
                    'description': '触发紧急止损',
                    'value': total_loss_ratio,
                    'limit': SystemConfig.EMERGENCY_STOP_LOSS
                })
                print("紧急停止: 总亏损比例 %.1f%% 超过限制 %.1f%%" %
                      (total_loss_ratio * 100, SystemConfig.EMERGENCY_STOP_LOSS * 100))
                return True

            return False

        except Exception as e:
            print("检查紧急停止异常: %s" % str(e))
            return False

    def calculate_stop_loss_price(self, entry_price: float) -> float:
        """计算止损价格"""
        return entry_price * (1 - SystemConfig.STOP_LOSS_RATIO)

    def calculate_stop_profit_price(self, entry_price: float) -> float:
        """计算止盈价格"""
        return entry_price * (1 + SystemConfig.STOP_PROFIT_RATIO)

    def should_stop_loss(self, stock_code: str, current_price: float, entry_price: float) -> bool:
        """判断是否应该止损"""
        try:
            stop_loss_price = self.calculate_stop_loss_price(entry_price)

            if current_price <= stop_loss_price:
                loss_ratio = (entry_price - current_price) / entry_price
                print("触发止损: %s, 当前价 %.2f, 止损价 %.2f, 亏损 %.1f%%" %
                      (stock_code, current_price, stop_loss_price, loss_ratio * 100))
                return True

            return False

        except Exception as e:
            print("判断止损异常: %s" % str(e))
            return False

    def should_stop_profit(self, stock_code: str, current_price: float, entry_price: float) -> bool:
        """判断是否应该止盈"""
        try:
            stop_profit_price = self.calculate_stop_profit_price(entry_price)

            if current_price >= stop_profit_price:
                profit_ratio = (current_price - entry_price) / entry_price
                print("触发止盈: %s, 当前价 %.2f, 止盈价 %.2f, 盈利 %.1f%%" %
                      (stock_code, current_price, stop_profit_price, profit_ratio * 100))
                return True

            return False

        except Exception as e:
            print("判断止盈异常: %s" % str(e))
            return False

# ==================== 日志管理模块 ====================
class LogManager:
    """日志管理器 - 负责记录和管理系统日志"""

    def __init__(self):
        self.system_logs = []
        self.max_logs = 1000  # 最大日志条数

    def log_info(self, message: str):
        """记录信息日志"""
        log_entry = {
            'timestamp': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'level': 'INFO',
            'message': message
        }
        self.system_logs.append(log_entry)
        self._trim_logs()
        print("[INFO] %s" % message)

    def log_warning(self, message: str):
        """记录警告日志"""
        log_entry = {
            'timestamp': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'level': 'WARNING',
            'message': message
        }
        self.system_logs.append(log_entry)
        self._trim_logs()
        print("[WARNING] %s" % message)

    def log_error(self, message: str):
        """记录错误日志"""
        log_entry = {
            'timestamp': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'level': 'ERROR',
            'message': message
        }
        self.system_logs.append(log_entry)
        self._trim_logs()
        print("[ERROR] %s" % message)

    def log_trade(self, action: str, stock_code: str, price: float, quantity: int, amount: float):
        """记录交易日志"""
        message = "交易执行: %s %s, 价格: %.2f, 数量: %d, 金额: %.2f" % (
            action, stock_code, price, quantity, amount)
        self.log_info(message)

    def _trim_logs(self):
        """清理过多的日志"""
        if len(self.system_logs) > self.max_logs:
            self.system_logs = self.system_logs[-self.max_logs:]

# ==================== 主系统类 ====================
class ZhangTingAutoTradingSystem:
    """涨停双响炮自动交易系统主类"""

    def __init__(self):
        self.data_manager = DataManager()
        self.trading_engine = TradingEngine()
        self.risk_manager = RiskManager()
        self.log_manager = LogManager()

        self.system_start_time = datetime.datetime.now()
        self.last_status_time = 0
        self.system_running = True

        self.log_manager.log_info("涨停双响炮自动交易系统初始化完成")

    def initialize_system(self) -> bool:
        """初始化系统"""
        try:
            self.log_manager.log_info("系统初始化开始...")

            # 检查配置
            self.log_manager.log_info("目标板块: %s" % SystemConfig.TARGET_SECTOR_NAME)
            self.log_manager.log_info("交易模式: %s" % ("实盘" if SystemConfig.ENABLE_REAL_TRADING else "模拟"))
            self.log_manager.log_info("最大单股金额: %d元" % SystemConfig.MAX_SINGLE_STOCK_AMOUNT)

            # 初始化板块数据
            success = self.data_manager.update_sector_data()
            if success:
                self.log_manager.log_info("板块数据初始化成功")
                return True
            else:
                self.log_manager.log_error("板块数据初始化失败")
                return False

        except Exception as e:
            self.log_manager.log_error("系统初始化异常: %s" % str(e))
            return False

    def process_sector_changes(self):
        """处理板块变化"""
        try:
            # 检查紧急停止
            if self.risk_manager.emergency_stop:
                self.log_manager.log_warning("系统处于紧急停止状态")
                return

            # 更新板块数据
            if not self.data_manager.update_sector_data():
                return

            # 检测变化
            changes = self.data_manager.detect_stock_changes()

            if not changes:
                return

            self.log_manager.log_info("检测到板块变化")

            # 处理变化
            for sector_name, sector_changes in changes.items():
                # 处理新增股票 - 买入
                for stock_code in sector_changes.get('added', []):
                    if self.data_manager.validate_stock_code(stock_code):
                        if self.trading_engine.should_buy_stock(stock_code):
                            success = self.trading_engine.execute_buy_order(stock_code)
                            if success:
                                self.log_manager.log_info("买入成功: %s" % stock_code)
                            else:
                                self.log_manager.log_warning("买入失败: %s" % stock_code)

                # 处理移除股票 - 卖出
                for stock_code in sector_changes.get('removed', []):
                    if self.trading_engine.should_sell_stock(stock_code):
                        success = self.trading_engine.execute_sell_order(stock_code, "sector_remove")
                        if success:
                            self.log_manager.log_info("卖出成功: %s" % stock_code)
                        else:
                            self.log_manager.log_warning("卖出失败: %s" % stock_code)

        except Exception as e:
            self.log_manager.log_error("处理板块变化异常: %s" % str(e))

    def check_stop_loss_profit(self):
        """检查止损止盈"""
        try:
            for stock_code, position in self.trading_engine.current_positions.items():
                entry_price = position['avg_price']
                # 这里应该获取实时价格，暂时用模拟价格
                current_price = entry_price * 1.01  # 假设上涨1%

                # 检查止损
                if self.risk_manager.should_stop_loss(stock_code, current_price, entry_price):
                    success = self.trading_engine.execute_sell_order(stock_code, "stop_loss")
                    if success:
                        self.log_manager.log_warning("止损卖出: %s" % stock_code)

                # 检查止盈
                elif self.risk_manager.should_stop_profit(stock_code, current_price, entry_price):
                    success = self.trading_engine.execute_sell_order(stock_code, "stop_profit")
                    if success:
                        self.log_manager.log_info("止盈卖出: %s" % stock_code)

        except Exception as e:
            self.log_manager.log_error("检查止损止盈异常: %s" % str(e))

    def print_system_status(self):
        """打印系统状态"""
        try:
            now = datetime.datetime.now()
            running_time = now - self.system_start_time

            stats = self.trading_engine.get_today_stats()

            print("\n" + "="*50)
            print("涨停双响炮自动交易系统状态")
            print("="*50)
            print("当前时间: %s" % now.strftime('%Y-%m-%d %H:%M:%S'))
            print("运行时长: %s" % str(running_time).split('.')[0])
            print("交易时间: %s" % ("是" if self.data_manager.is_trading_time() else "否"))
            print("目标板块: %s" % SystemConfig.TARGET_SECTOR_NAME)
            print("交易模式: %s" % ("实盘" if SystemConfig.ENABLE_REAL_TRADING else "模拟"))
            print("-" * 50)
            print("今日买入: %d笔" % stats['buy_count'])
            print("今日卖出: %d笔" % stats['sell_count'])
            print("当前持仓: %d只" % stats['current_positions'])
            print("今日盈亏: %.2f元" % stats['total_profit'])
            print("交易次数: %d/%d" % (stats['trade_count'], SystemConfig.MAX_TRADES_PER_DAY))
            print("紧急停止: %s" % ("是" if self.risk_manager.emergency_stop else "否"))

            # 显示持仓详情
            if self.trading_engine.current_positions:
                print("-" * 50)
                print("持仓详情:")
                for stock_code, pos in self.trading_engine.current_positions.items():
                    print("  %s: %d股 @%.2f" % (stock_code, pos['quantity'], pos['avg_price']))

            print("="*50 + "\n")

        except Exception as e:
            self.log_manager.log_error("打印系统状态异常: %s" % str(e))

    def run_main_loop(self):
        """运行主循环"""
        try:
            current_time = time.time()

            # 检查是否在交易时间内
            if not self.data_manager.is_trading_time():
                return

            # 处理板块变化
            self.process_sector_changes()

            # 检查止损止盈
            self.check_stop_loss_profit()

            # 定期打印状态
            if current_time - self.last_status_time > SystemConfig.STATUS_REPORT_INTERVAL:
                self.last_status_time = current_time
                self.print_system_status()

        except Exception as e:
            self.log_manager.log_error("主循环异常: %s" % str(e))

# ==================== QMT策略接口 ====================

# 全局变量
g_trading_system = None
g_initialization_complete = False

def init(ContextInfo):
    """QMT策略初始化函数"""
    global g_trading_system, g_initialization_complete

    print("=" * 60)
    print("涨停双响炮自动交易系统")
    print("=" * 60)
    print("版本: 1.0.0")
    print("目标板块: %s" % SystemConfig.TARGET_SECTOR_NAME)
    print("交易模式: %s" % ("实盘" if SystemConfig.ENABLE_REAL_TRADING else "模拟"))
    print("=" * 60)

    try:
        # 创建交易系统实例
        g_trading_system = ZhangTingAutoTradingSystem()

        # 初始化系统
        success = g_trading_system.initialize_system()

        if success:
            g_initialization_complete = True
            print("系统初始化成功，开始监控板块变化...")
        else:
            print("系统初始化失败，请检查配置和网络连接")

    except Exception as e:
        print("系统初始化异常: %s" % str(e))

def handlebar(ContextInfo):
    """QMT策略主循环函数"""
    global g_trading_system, g_initialization_complete

    try:
        if not g_initialization_complete or g_trading_system is None:
            return

        # 运行主循环
        g_trading_system.run_main_loop()

    except Exception as e:
        print("策略运行异常: %s" % str(e))
