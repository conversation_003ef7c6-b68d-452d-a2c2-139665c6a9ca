<?xml version="1.0" encoding="UTF-8"?>
<root>
    <strategy>
        <name>QMT涨停双响炮自动交易系统</name>
        <version>1.0.0</version>
        <description>基于手动配置股票列表的自动交易系统</description>
        <author>QMT量化团队</author>
    </strategy>
    
    <layout>
        <window width="900" height="700" title="QMT涨停双响炮自动交易系统">
            <panel name="股票配置" x="10" y="10" width="870" height="150">
                <control type="label" x="10" y="10" width="100" height="25" text="目标股票列表:" />
                <control type="memo" x="10" y="35" width="850" height="80" name="TARGET_STOCKS" 
                         text="请在此输入股票代码，每行一个，格式如：000001.SZ" />
                <control type="label" x="10" y="120" width="400" height="25" 
                         text="说明：请在上方输入框中添加要监控的股票代码，每行一个" />
                <control type="button" x="750" y="120" width="100" height="25" name="LOAD_STOCKS" text="加载股票" />
            </panel>
            
            <panel name="基础配置" x="10" y="170" width="430" height="280">
                <control type="label" x="10" y="10" width="100" height="25" text="交易模式:" />
                <control type="combo" x="120" y="10" width="240" height="25" name="TRADING_MODE">
                    <item value="0" text="模拟交易" />
                    <item value="1" text="实盘交易" />
                </control>
                
                <control type="label" x="10" y="45" width="100" height="25" text="单股最大金额:" />
                <control type="edit" x="120" y="45" width="240" height="25" name="MAX_SINGLE_AMOUNT" value="10000" />
                
                <control type="label" x="10" y="80" width="100" height="25" text="每日最大亏损:" />
                <control type="edit" x="120" y="80" width="240" height="25" name="MAX_DAILY_LOSS" value="5000" />
                
                <control type="label" x="10" y="115" width="100" height="25" text="止损比例(%):" />
                <control type="edit" x="120" y="115" width="240" height="25" name="STOP_LOSS_RATIO" value="5" />
                
                <control type="label" x="10" y="150" width="100" height="25" text="止盈比例(%):" />
                <control type="edit" x="120" y="150" width="240" height="25" name="STOP_PROFIT_RATIO" value="12" />
                
                <control type="label" x="10" y="185" width="100" height="25" text="最大仓位比例(%):" />
                <control type="edit" x="120" y="185" width="240" height="25" name="MAX_POSITION_RATIO" value="15" />
                
                <control type="checkbox" x="10" y="220" width="200" height="25" name="EXCLUDE_ST_STOCKS" text="排除ST股票" checked="true" />
                
                <control type="label" x="10" y="250" width="100" height="25" text="每日最大交易:" />
                <control type="edit" x="120" y="250" width="240" height="25" name="MAX_TRADES_PER_DAY" value="30" />
            </panel>
            
            <panel name="时间配置" x="450" y="170" width="430" height="280">
                <control type="label" x="10" y="10" width="100" height="25" text="交易开始时间:" />
                <control type="edit" x="120" y="10" width="240" height="25" name="TRADING_START_TIME" value="09:30:00" />
                
                <control type="label" x="10" y="45" width="100" height="25" text="交易结束时间:" />
                <control type="edit" x="120" y="45" width="240" height="25" name="TRADING_END_TIME" value="14:55:00" />
                
                <control type="label" x="10" y="80" width="100" height="25" text="午休开始时间:" />
                <control type="edit" x="120" y="80" width="240" height="25" name="LUNCH_BREAK_START" value="11:30:00" />
                
                <control type="label" x="10" y="115" width="100" height="25" text="午休结束时间:" />
                <control type="edit" x="120" y="115" width="240" height="25" name="LUNCH_BREAK_END" value="13:00:00" />
                
                <control type="label" x="10" y="150" width="100" height="25" text="数据更新间隔(秒):" />
                <control type="edit" x="120" y="150" width="240" height="25" name="DATA_UPDATE_INTERVAL" value="3" />
                
                <control type="label" x="10" y="185" width="100" height="25" text="状态报告间隔(秒):" />
                <control type="edit" x="120" y="185" width="240" height="25" name="STATUS_REPORT_INTERVAL" value="60" />
                
                <control type="label" x="10" y="220" width="100" height="25" text="最低股价限制:" />
                <control type="edit" x="120" y="220" width="240" height="25" name="MIN_STOCK_PRICE" value="3.0" />
                
                <control type="label" x="10" y="250" width="100" height="25" text="最高股价限制:" />
                <control type="edit" x="120" y="250" width="240" height="25" name="MAX_STOCK_PRICE" value="200.0" />
            </panel>
            
            <panel name="系统状态" x="10" y="460" width="870" height="150">
                <control type="label" x="10" y="10" width="100" height="25" text="系统状态:" />
                <control type="label" x="120" y="10" width="200" height="25" name="SYSTEM_STATUS" text="未启动" />
                
                <control type="label" x="10" y="40" width="100" height="25" text="目标股票数:" />
                <control type="label" x="120" y="40" width="100" height="25" name="TARGET_STOCK_COUNT" text="0" />
                
                <control type="label" x="250" y="40" width="100" height="25" text="当前持仓:" />
                <control type="label" x="350" y="40" width="100" height="25" name="CURRENT_POSITIONS" text="0" />
                
                <control type="label" x="10" y="70" width="100" height="25" text="今日买入:" />
                <control type="label" x="120" y="70" width="100" height="25" name="TODAY_BUY_COUNT" text="0" />
                
                <control type="label" x="250" y="70" width="100" height="25" text="今日卖出:" />
                <control type="label" x="350" y="70" width="100" height="25" name="TODAY_SELL_COUNT" text="0" />
                
                <control type="label" x="10" y="100" width="100" height="25" text="今日盈亏:" />
                <control type="label" x="120" y="100" width="100" height="25" name="TODAY_PROFIT" text="0.00" />
                
                <control type="label" x="250" y="100" width="100" height="25" text="交易次数:" />
                <control type="label" x="350" y="100" width="100" height="25" name="TRADE_COUNT" text="0" />
                
                <control type="label" x="500" y="10" width="100" height="25" text="运行时长:" />
                <control type="label" x="600" y="10" width="200" height="25" name="RUNNING_TIME" text="00:00:00" />
                
                <control type="label" x="500" y="40" width="100" height="25" text="交易时间:" />
                <control type="label" x="600" y="40" width="100" height="25" name="TRADING_TIME" text="否" />
                
                <control type="label" x="500" y="70" width="100" height="25" text="紧急停止:" />
                <control type="label" x="600" y="70" width="100" height="25" name="EMERGENCY_STOP" text="否" />
            </panel>
            
            <panel name="操作按钮" x="10" y="620" width="870" height="50">
                <control type="button" x="10" y="10" width="100" height="30" name="START_SYSTEM" text="启动系统" />
                <control type="button" x="120" y="10" width="100" height="30" name="STOP_SYSTEM" text="停止系统" />
                <control type="button" x="230" y="10" width="100" height="30" name="EMERGENCY_STOP" text="紧急停止" />
                <control type="button" x="340" y="10" width="100" height="30" name="REFRESH_DATA" text="刷新数据" />
                <control type="button" x="450" y="10" width="100" height="30" name="EXPORT_LOG" text="导出日志" />
                <control type="button" x="560" y="10" width="100" height="30" name="SAVE_CONFIG" text="保存配置" />
                <control type="button" x="670" y="10" width="100" height="30" name="LOAD_CONFIG" text="加载配置" />
                <control type="button" x="780" y="10" width="80" height="30" name="HELP" text="帮助" />
            </panel>
        </window>
    </layout>
    
    <events>
        <event name="START_SYSTEM" type="click" action="start_trading_system" />
        <event name="STOP_SYSTEM" type="click" action="stop_trading_system" />
        <event name="EMERGENCY_STOP" type="click" action="emergency_stop_system" />
        <event name="REFRESH_DATA" type="click" action="refresh_stock_data" />
        <event name="LOAD_STOCKS" type="click" action="load_target_stocks" />
        <event name="EXPORT_LOG" type="click" action="export_trading_log" />
        <event name="SAVE_CONFIG" type="click" action="save_system_config" />
        <event name="LOAD_CONFIG" type="click" action="load_system_config" />
        <event name="HELP" type="click" action="show_help_dialog" />
    </events>
    
    <parameters>
        <param name="TARGET_STOCKS" type="string" default="" description="目标股票代码列表，每行一个" />
        <param name="TRADING_MODE" type="int" default="0" description="交易模式：0=模拟，1=实盘" />
        <param name="MAX_SINGLE_AMOUNT" type="int" default="10000" description="单只股票最大买入金额" />
        <param name="MAX_DAILY_LOSS" type="int" default="5000" description="每日最大亏损限制" />
        <param name="STOP_LOSS_RATIO" type="float" default="5.0" description="止损比例（百分比）" />
        <param name="STOP_PROFIT_RATIO" type="float" default="12.0" description="止盈比例（百分比）" />
        <param name="MAX_POSITION_RATIO" type="float" default="15.0" description="单只股票最大仓位比例（百分比）" />
        <param name="TRADING_START_TIME" type="string" default="09:30:00" description="交易开始时间" />
        <param name="TRADING_END_TIME" type="string" default="14:55:00" description="交易结束时间" />
        <param name="LUNCH_BREAK_START" type="string" default="11:30:00" description="午休开始时间" />
        <param name="LUNCH_BREAK_END" type="string" default="13:00:00" description="午休结束时间" />
        <param name="DATA_UPDATE_INTERVAL" type="int" default="3" description="数据更新间隔（秒）" />
        <param name="STATUS_REPORT_INTERVAL" type="int" default="60" description="状态报告间隔（秒）" />
        <param name="MAX_TRADES_PER_DAY" type="int" default="30" description="每日最大交易次数" />
        <param name="MIN_STOCK_PRICE" type="float" default="3.0" description="最低股价限制" />
        <param name="MAX_STOCK_PRICE" type="float" default="200.0" description="最高股价限制" />
        <param name="EXCLUDE_ST_STOCKS" type="bool" default="true" description="是否排除ST股票" />
    </parameters>
    
    <help>
        <section title="使用说明">
            <content>
                1. 在"股票配置"面板中输入要监控的股票代码，每行一个
                2. 配置交易参数，建议先使用模拟模式测试
                3. 点击"启动系统"开始监控股票变化
                4. 系统会根据股票列表变化自动执行买卖操作
                5. 可通过"紧急停止"按钮立即停止所有交易
            </content>
        </section>
        
        <section title="股票代码格式">
            <content>
                正确格式示例：
                000001.SZ  (深圳平安银行)
                000002.SZ  (深圳万科A)
                600000.SH  (上海浦发银行)
                600036.SH  (上海招商银行)
                
                注意：
                - 深圳股票以.SZ结尾
                - 上海股票以.SH结尾
                - 每行只能输入一个股票代码
            </content>
        </section>
        
        <section title="注意事项">
            <content>
                1. 实盘交易前请充分测试策略有效性
                2. 合理设置止损止盈比例，控制风险
                3. 关注每日亏损限制，避免过度损失
                4. 手动修改股票列表来模拟板块变化
                5. 股票交易有风险，投资需谨慎
            </content>
        </section>
        
        <section title="技术支持">
            <content>
                如遇问题请检查：
                1. 股票代码格式是否正确
                2. QMT版本是否支持Python策略
                3. 网络连接和数据权限是否正常
                4. 查看系统日志获取详细错误信息
            </content>
        </section>
    </help>
</root>
