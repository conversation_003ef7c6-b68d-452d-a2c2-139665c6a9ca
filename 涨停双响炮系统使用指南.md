# QMT涨停双响炮自动交易系统使用指南

## 🎯 系统概述

**QMT涨停双响炮自动交易系统**是基于QMT平台开发的专业量化交易系统，通过手动配置目标股票列表，监控股票变化并实现自动化买卖操作。

### 核心特性
- ✅ **手动配置**：通过代码或XML界面配置目标股票列表
- ✅ **智能监控**：实时监控目标股票列表变化
- ✅ **自动交易**：新增股票自动买入，移除股票自动卖出
- ✅ **风险控制**：完善的止损止盈和资金管理机制
- ✅ **模拟实盘**：支持模拟和实盘两种交易模式
- ✅ **界面友好**：配套XML界面，操作简单直观

## 🚀 快速开始

### 第一步：环境准备
1. **确保QMT平台正常运行**
   - QMT版本：3.0或以上
   - 确认Python策略功能可用
   - 验证网络连接正常

2. **准备目标股票列表**
   - 确定要监控的股票代码
   - 准备股票代码列表（格式：000001.SZ, 600000.SH等）

### 第二步：系统部署
1. **复制策略文件**
   - 在QMT中新建Python策略
   - 复制`QMT涨停双响炮自动交易系统.py`的全部代码
   - 粘贴到QMT策略编辑器中

2. **配置XML界面（可选）**
   - 将`QMT涨停双响炮自动交易系统.xml`文件复制到QMT安装目录下的`python/formulaLayout`文件夹
   - 如果文件夹不存在，请手动创建
   - XML文件提供图形化配置界面

3. **配置目标股票列表**
   - 在代码中的`SystemConfig.TARGET_STOCKS`中添加股票代码
   - 或通过XML界面输入股票代码

4. **编译运行**
   - 在QMT中编译策略
   - 确认无编译错误
   - 启动策略运行

### 第三步：股票列表配置
**方法1：在代码中配置**
在`SystemConfig`类中修改`TARGET_STOCKS`列表：

```python
# 目标股票列表配置
TARGET_STOCKS = [
    "000001.SZ",  # 平安银行
    "000002.SZ",  # 万科A
    "600000.SH",  # 浦发银行
    "600036.SH",  # 招商银行
    # 添加更多股票代码...
]
```

**方法2：通过XML界面配置**
- 在XML界面的"股票配置"面板中输入股票代码
- 每行一个股票代码
- 点击"加载股票"按钮

### 第四步：其他参数配置
```python
# 交易配置
ENABLE_REAL_TRADING = False              # 交易模式（False=模拟，True=实盘）
MAX_SINGLE_STOCK_AMOUNT = 10000         # 单只股票最大买入金额
MAX_DAILY_LOSS = 5000                   # 每日最大亏损限制
STOP_LOSS_RATIO = 0.05                  # 止损比例（5%）
STOP_PROFIT_RATIO = 0.12                # 止盈比例（12%）
```

## 📊 系统功能详解

### 1. 板块监控机制
- **实时监控**：每3秒检查一次板块变化
- **变化检测**：自动识别新增和移除的股票
- **智能过滤**：排除ST股票和不符合条件的股票

### 2. 自动交易逻辑
```
板块新增股票 → 生成买入信号 → 风险检查 → 执行买入
板块移除股票 → 生成卖出信号 → 风险检查 → 执行卖出
```

### 3. 风险控制体系
- **资金管理**：单只股票最大仓位限制
- **止损止盈**：自动计算止损止盈价格
- **每日限制**：每日最大亏损和交易次数限制
- **紧急停止**：达到风险阈值自动停止交易

### 4. 日志记录系统
- **交易日志**：详细记录每笔买卖操作
- **系统日志**：记录系统运行状态和异常
- **统计报告**：每日交易统计和盈亏分析

## 🎛️ 系统状态监控

### 控制台输出示例
```
==================================================
涨停双响炮自动交易系统状态
==================================================
当前时间: 2025-01-15 10:30:15
运行时长: 1:25:30
交易时间: 是
目标板块: 涨停双响炮刚启动
交易模式: 模拟
--------------------------------------------------
今日买入: 3笔
今日卖出: 1笔
当前持仓: 2只
今日盈亏: 156.80元
交易次数: 4/30
紧急停止: 否
--------------------------------------------------
持仓详情:
  000001.SZ: 500股 @10.20
  000002.SZ: 400股 @12.50
==================================================
```

### 关键指标说明
- **交易时间**：显示当前是否在交易时间内
- **今日买入/卖出**：当日执行的买卖笔数
- **当前持仓**：实时持仓股票数量
- **今日盈亏**：当日累计盈亏金额
- **交易次数**：已执行交易次数/每日限制

## ⚠️ 重要注意事项

### 安全提醒
1. **默认模拟模式**
   - 系统默认运行在模拟模式
   - 实盘前必须充分测试
   - 确认策略有效性后再启用实盘

2. **板块名称匹配**
   - 板块名称必须完全匹配："涨停双响炮刚启动"
   - 区分大小写和标点符号
   - 建议在同花顺中直接复制板块名称

3. **风险控制**
   - 合理设置止损止盈比例
   - 控制单只股票投入金额
   - 关注每日亏损限制

### 使用建议
1. **测试阶段**
   - 先在模拟模式下运行1-2周
   - 观察系统稳定性和交易逻辑
   - 根据实际情况调整参数

2. **实盘部署**
   - 从小额资金开始
   - 逐步增加投入金额
   - 密切监控系统运行状态

3. **日常维护**
   - 定期检查板块数据更新
   - 关注系统日志和异常信息
   - 根据市场情况调整策略参数

## 🔧 故障排除

### 常见问题及解决方案

#### 1. 板块数据获取失败
**现象**：提示"板块数据更新失败"
**解决方案**：
- 检查板块名称是否正确
- 确认同花顺中板块是否存在
- 验证QMT网络连接
- 重启QMT平台

#### 2. 系统无法启动
**现象**：初始化失败或编译错误
**解决方案**：
- 检查代码完整性
- 确认QMT版本支持Python策略
- 验证代码语法正确性
- 查看错误日志详细信息

#### 3. 交易不执行
**现象**：检测到板块变化但不执行交易
**解决方案**：
- 检查是否在交易时间内
- 确认未达到每日交易限制
- 验证风险控制参数设置
- 检查股票是否已持仓

#### 4. XML界面不显示
**现象**：策略运行正常但无图形界面
**解决方案**：
- 确认XML文件已复制到正确位置
- 检查`python/formulaLayout`文件夹是否存在
- 重启QMT平台
- 验证XML文件格式正确性

## 📞 技术支持

### 获取帮助
1. **查看系统日志**
   - 关注控制台输出信息
   - 记录错误信息和时间
   - 分析系统运行状态

2. **参数调试**
   - 适当调整更新间隔
   - 修改风险控制参数
   - 测试不同配置组合

3. **社区支持**
   - QMT官方论坛
   - 量化交易社区
   - 技术交流群组

### 版本更新
- 定期检查系统更新
- 关注QMT平台变化
- 备份重要配置和数据

## 📈 系统优化建议

### 性能优化
1. **数据更新频率**：根据网络状况调整更新间隔
2. **日志管理**：定期清理过多的日志记录
3. **内存使用**：长期运行时注意内存占用

### 策略优化
1. **参数调整**：根据历史数据优化止损止盈比例
2. **风险控制**：根据市场波动调整风险参数
3. **时间管理**：优化交易时间段设置

---

**免责声明**：
本系统仅供学习和研究使用。股票交易存在风险，使用者应当充分了解相关风险并承担相应责任。实盘交易前请务必进行充分测试。
