# QMT自动交易系统部署使用指南

## 🚀 快速开始

### 第一步：环境准备
1. **确保已安装QMT平台**
   - 版本要求：QMT 3.0或以上
   - 确认Python策略功能可用
   - 测试基本的API功能

2. **准备同花顺软件**
   - 安装同花顺软件
   - 创建自定义板块
   - 确认板块数据导出路径

### 第二步：选择部署版本

#### 🔧 简化版（推荐新手）
**文件：** `QMT自动交易系统简化版.py`

**特点：**
- 代码简洁，易于理解
- 配置简单，参数集中
- 适合学习和测试
- 功能完整但不复杂

**使用步骤：**
1. 在QMT中新建Python策略
2. 复制`QMT自动交易系统简化版.py`的代码
3. 修改配置参数：
   ```python
   SECTOR_PATH = "C:/同花顺/板块数据/"  # 修改为实际路径
   MAX_AMOUNT = 5000                    # 单只股票最大金额
   UPDATE_INTERVAL = 10                 # 更新间隔
   ENABLE_TRADING = False               # 模拟模式
   ```
4. 编译并运行

#### 🏢 完整版（推荐专业用户）
**文件：** `QMT自动交易系统完整版.py`

**特点：**
- 功能完整，模块化设计
- 支持复杂配置
- 包含完整的风险管理
- 适合生产环境

**使用步骤：**
1. 在QMT中新建Python策略
2. 复制`QMT自动交易系统完整版.py`的代码
3. 根据需要修改配置（在代码中的ConfigManager类）
4. 编译并运行

### 第三步：配置参数

#### 核心配置项
```python
# 数据配置
"sector_path": "C:/同花顺/板块数据/"    # 板块数据路径
"update_interval": 10                   # 数据更新间隔（秒）

# 交易配置
"max_single_stock_amount": 5000         # 单只股票最大金额
"max_daily_loss": 2000                  # 每日最大亏损
"stop_loss_ratio": 0.03                 # 止损比例（3%）
"stop_profit_ratio": 0.08               # 止盈比例（8%）

# 风险配置
"max_position_ratio": 0.1               # 单只股票最大仓位比例
"emergency_stop_loss": 0.1              # 紧急止损比例
```

#### 时间配置
```python
"trading_start": "09:30:00"             # 交易开始时间
"trading_end": "15:00:00"               # 交易结束时间
"lunch_break_start": "11:30:00"         # 午休开始时间
"lunch_break_end": "13:00:00"           # 午休结束时间
```

## 📋 使用流程

### 1. 准备板块数据
1. **在同花顺中创建自定义板块**
   - 打开同花顺软件
   - 创建自定义板块（如"我的板块1"）
   - 添加股票到板块中

2. **导出板块数据**
   - 找到同花顺的板块数据文件（通常为.blk格式）
   - 记录文件保存路径
   - 确保系统有读取权限

### 2. 启动系统
1. **在QMT中运行策略**
   - 新建Python策略
   - 粘贴代码并配置参数
   - 编译策略
   - 启动运行

2. **观察系统输出**
   ```
   === QMT自动交易系统启动 ===
   加载板块 我的板块1: 5只股票
   加载板块 我的板块2: 3只股票
   系统初始化完成，开始监控...
   ```

### 3. 监控运行
系统会自动：
- 定期检查板块变化
- 生成买卖信号
- 执行模拟交易
- 输出运行状态

## 📊 系统输出说明

### 正常运行输出
```
=== 系统状态 10:30:15 ===
交易时间: 是
板块数量: 3
今日买入: 2只
今日卖出: 1只
当前持仓: 1只
交易次数: 3
========================
```

### 板块变化检测
```
板块 我的板块1 新增: 000001.SZ, 000002.SZ
模拟买入: 000001.SZ (板块: 我的板块1) 价格: 10.00 数量: 500 金额: 5000.00
```

### 交易执行
```
模拟买入: 000001.SZ 价格: 10.50 数量: 400 金额: 4200.00
模拟卖出: 000002.SZ 价格: 11.20 数量: 400 金额: 4480.00 盈亏: 280.00
```

## ⚠️ 重要注意事项

### 安全提醒
1. **默认为模拟模式**
   - 系统默认运行在模拟模式
   - 不会执行真实交易
   - 仅用于测试和学习

2. **实盘交易前必须**
   - 充分测试系统功能
   - 验证板块数据准确性
   - 确认风险控制参数
   - 小额资金测试

### 风险控制
1. **资金管理**
   - 设置合理的单只股票最大金额
   - 控制总体仓位比例
   - 设置每日最大亏损限制

2. **止损止盈**
   - 系统会自动计算止损止盈价格
   - 建议根据市场情况调整比例
   - 关注风险事件提醒

## 🔧 故障排除

### 常见问题

#### 1. 板块数据读取失败
**现象：** `板块数据路径不存在`
**解决：**
- 检查SECTOR_PATH路径是否正确
- 确认同花顺板块文件存在
- 检查文件读取权限

#### 2. 股票代码格式错误
**现象：** `股票代码格式无效`
**解决：**
- 确保股票代码格式为：000001.SZ 或 600000.SH
- 检查板块文件内容格式
- 清理文件中的特殊字符

#### 3. 系统不执行交易
**现象：** 系统运行但无交易
**解决：**
- 检查是否在交易时间内
- 确认板块数据有变化
- 检查股票是否已持仓或已交易

#### 4. 编译错误
**现象：** QMT编译失败
**解决：**
- 确保代码开头有 `#coding:gbk`
- 检查函数名：`init(ContextInfo)` 和 `handlebar(ContextInfo)`
- 验证代码语法正确性

### 调试技巧
1. **查看输出日志**
   - 关注系统输出信息
   - 记录错误信息
   - 分析运行状态

2. **逐步测试**
   - 先测试板块数据读取
   - 再测试变化检测
   - 最后测试交易逻辑

3. **参数调整**
   - 适当调整更新间隔
   - 修改交易金额限制
   - 调整风险控制参数

## 📞 技术支持

### 获取帮助
1. **查看日志输出**
   - 系统会输出详细的运行信息
   - 记录错误信息用于分析

2. **参考测试用例**
   - 查看 `tests/` 目录下的测试文件
   - 了解各模块的预期行为

3. **社区支持**
   - QMT官方论坛
   - 量化交易社区
   - GitHub项目页面

### 版本更新
- 定期检查系统更新
- 关注QMT平台变化
- 备份重要配置和数据

---

**免责声明：**
本系统仅供学习和研究使用。实盘交易存在风险，请谨慎使用。使用者应当充分了解相关风险，并承担相应责任。
