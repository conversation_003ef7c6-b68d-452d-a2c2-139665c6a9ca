# 国金证券QMT自动交易系统方案计划

## 一、系统概述
本系统将开发一个基于国金证券QMT平台的自动交易程序，能够读取同花顺自定义板块并根据预设策略进行自动买卖操作，同时包含完善的风险控制和交易记录功能。

## 二、核心功能模块
1. **数据获取模块**
   - 定时读取同花顺自定义板块
   - 实时获取股票行情数据
   - 监控市场整体情况

2. **交易决策模块**
   - 买入信号判断
   - 卖出信号判断
   - 交易限制控制（今日已买卖股票限制）

3. **资金管理模块**
   - 单只股票买入金额控制
   - 总资金使用率控制
   - 持仓比例管理

4. **风险控制模块**
   - 每日最大亏损限制
   - 单只股票最大仓位限制
   - 交易时间段控制

5. **记录与日志模块**
   - 交易记录保存
   - 操作日志记录
   - 每日交易统计

## 三、程序流程设计
1. 初始化系统参数和配置
2. 建立与交易接口的连接
3. 启动定时任务，每秒刷新一次数据
4. 读取并解析同花顺自定义板块
5. 对板块内股票进行筛选和分析
6. 根据策略生成交易信号
7. 执行交易操作并记录
8. 实时监控持仓和市场情况
9. 每日交易结束后生成报告

## 四、完整代码实现

下面是基于上述方案的完整.rzrk文件代码实现：





## 五、代码说明

1. **系统架构**：
   代码采用模块化设计，包含初始化模块、数据获取模块、交易决策模块、风险控制模块和日志记录模块，各模块职责清晰，便于维护和扩展。

2. **核心功能实现**：
   - 同花顺板块读取：通过解析指定目录下的.blk文件获取自定义板块股票
   - 自动交易：实现了买入和卖出的完整流程，包括委托下单和成交确认
   - 交易限制：实现了"今日已买入股票不再买入"和"今日已卖出股票今日不再买回"的限制
   - 资金管理：控制单只股票买入金额和总资金使用率

3. **扩展功能**：
   - 风险控制：设置了每日最大亏损限制
   - 止盈止损：可自定义止盈止损比例
   - 交易时间控制：只在设定的交易时间段内进行交易
   - 股票过滤：可过滤ST股票等不符合条件的股票
   - 详细日志：记录所有交易操作和系统状态

## 六、使用说明

1. 在使用前，需要替换代码中的账户信息（账号、密码）
2. 可以通过修改配置参数调整交易策略和风险控制指标
3. 程序会自动在指定路径生成交易日志和记录文件
4. 首次运行会使用默认配置，可通过创建auto_trade_config.json文件自定义配置

请注意，股票交易有风险，自动交易系统需要经过充分测试后再用于实盘交易。建议先在模拟环境中验证系统的稳定性和策略的有效性。